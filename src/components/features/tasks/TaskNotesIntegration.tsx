/**
 * Task Notes Integration Component
 *
 * Integrates notes functionality into task views with note editing
 * and seamless task-note management.
 */

import { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Stack,
  Chip,
} from '@mui/material';
import {
  Notes as NotesIcon,
} from '@mui/icons-material';
import { Task } from '../../../types/task';
import { TaskNote } from '../../../types/notes';
import { NotesList } from '../notes';
import { useTaskNotes } from '../../../hooks/useTaskNotes';

interface TaskNotesIntegrationProps {
  task: Task;
  timeEntryId?: string | null; // Optional time entry context
}

export function TaskNotesIntegration({
  task,
  timeEntryId,
}: TaskNotesIntegrationProps) {
  const [editingNote, setEditingNote] = useState<TaskNote | null>(null);

  // Hooks for notes
  const {
    notes,
    deleteNote,
    getNotesStats,
    getNotesByTimeEntryId,
  } = useTaskNotes(task.id);

  // Filter notes based on context
  const filteredNotes = timeEntryId
    ? getNotesByTimeEntryId(timeEntryId)
    : notes.filter(note => note.taskId === task.id);

  const notesStats = getNotesStats();

  // Reset editor state when task changes
  useEffect(() => {
    setEditingNote(null);
  }, [task]);



  const handleDeleteNote = useCallback(async (noteId: string): Promise<void> => {
    await deleteNote(noteId);
    if (editingNote?.id === noteId) {
      setEditingNote(null);
    }
  }, [deleteNote, editingNote]);

  const handleEditNote = useCallback((note: TaskNote) => {
    setEditingNote(note);
  }, []);

  const handleCreateNote = useCallback(() => {
    setEditingNote(null);
  }, []);



  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {/* Header */}
      <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <NotesIcon color="primary" />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {timeEntryId ? 'Time Entry Notes' : 'Task Notes'}
          </Typography>
          <Chip
            label={`${filteredNotes.length} notes`}
            size="small"
            color="primary"
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
        {/* Notes List */}
        <NotesList
          taskId={task.id}
          notes={filteredNotes}
          onEditNote={handleEditNote}
          onDeleteNote={handleDeleteNote}
          onCreateNote={handleCreateNote}
        />

      {/* Notes Statistics */}
      {filteredNotes.length > 0 && (
        <Paper variant="outlined" sx={{ mt: 3, p: 2 }}>
          <Typography variant="subtitle2" gutterBottom color="text.primary">
            Notes Summary
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <Typography variant="body2" color="text.secondary">
              {timeEntryId ? 'Time Entry Notes' : 'Total Notes'}: {filteredNotes.length}
            </Typography>
            {filteredNotes.length > 0 && (
              <Typography variant="body2" color="text.secondary">
                Last Updated: {new Date(filteredNotes.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())[0].updatedAt).toLocaleDateString()}
              </Typography>
            )}
          </Stack>
        </Paper>
      )}
      </Box>
    </Box>
  );
}
